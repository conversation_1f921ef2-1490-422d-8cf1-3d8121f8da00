import 'package:flutter/material.dart';

class MessageListScreen extends StatelessWidget {
  const MessageListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('消息列表'),
        actions: [
          Switch(
            value: true,
            onChanged: (value) {
              // TODO: 实现通知开关
            },
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: const Center(
        child: Text('消息列表页面 - 待实现'),
      ),
    );
  }
}
