import 'package:flutter/material.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/filter_bar.dart';
import '../widgets/item_list.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'name';
  bool _isAscending = true;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _onSortChanged(String sortBy, bool isAscending) {
    setState(() {
      _sortBy = sortBy;
      _isAscending = isAscending;
    });
  }

  void _resetFilters() {
    setState(() {
      _searchQuery = '';
      _sortBy = 'name';
      _isAscending = true;
    });
    _searchController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // 搜索栏
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: SearchBarWidget(
                controller: _searchController,
                onChanged: _onSearchChanged,
                hintText: '搜索物品名称、型号、供应商...',
              ),
            ),

            // 筛选栏
            FilterBar(
              sortBy: _sortBy,
              isAscending: _isAscending,
              onSortChanged: _onSortChanged,
              onReset: _resetFilters,
            ),

            // 物品列表
            Expanded(
              child: ItemList(
                searchQuery: _searchQuery,
                sortBy: _sortBy,
                isAscending: _isAscending,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
