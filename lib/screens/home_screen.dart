import 'package:flutter/material.dart';
import '../widgets/warehouse_selector.dart';
import '../widgets/notification_icon.dart';
import '../widgets/dashboard_cards.dart';
import '../widgets/category_grid.dart';
import '../widgets/recent_items_list.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // 顶部栏
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: const Row(
                children: [
                  Expanded(child: WarehouseSelector()),
                  SizedBox(width: 16),
                  NotificationIcon(),
                ],
              ),
            ),

            // 主体内容
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),

                    // 数据看板
                    const DashboardCards(),

                    const SizedBox(height: 24),

                    // 分类标题
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        '分类管理',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 分类网格
                    const CategoryGrid(),

                    const SizedBox(height: 24),

                    // 最近操作标题
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        '最近操作',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 最近操作物品列表
                    const RecentItemsList(),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
