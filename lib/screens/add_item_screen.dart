import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../widgets/image_picker_widget.dart';
import '../widgets/quantity_selector.dart';
import 'batch_operation_screen.dart';

class AddItemScreen extends StatefulWidget {
  const AddItemScreen({super.key});

  @override
  State<AddItemScreen> createState() => _AddItemScreenState();
}

class _AddItemScreenState extends State<AddItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _englishNameController = TextEditingController();
  final _modelController = TextEditingController();
  final _priceController = TextEditingController();
  final _shelfLocationController = TextEditingController();

  String? _selectedCategory;
  String? _selectedSupplier;
  int _quantity = 1;
  List<XFile> _selectedImages = [];

  @override
  void dispose() {
    _nameController.dispose();
    _englishNameController.dispose();
    _modelController.dispose();
    _priceController.dispose();
    _shelfLocationController.dispose();
    super.dispose();
  }

  void _onImagesSelected(List<XFile> images) {
    setState(() {
      _selectedImages = images;
    });
  }

  void _onQuantityChanged(int quantity) {
    setState(() {
      _quantity = quantity;
    });
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      // TODO: 实现添加物品逻辑
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('物品添加成功')));
      _resetForm();
    }
  }

  void _resetForm() {
    _formKey.currentState!.reset();
    _nameController.clear();
    _englishNameController.clear();
    _modelController.clear();
    _priceController.clear();
    _shelfLocationController.clear();
    setState(() {
      _selectedCategory = null;
      _selectedSupplier = null;
      _quantity = 1;
      _selectedImages = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('添加物品'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BatchOperationScreen(),
                ),
              );
            },
            child: const Text('批量操作'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 物品名称
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: '物品名称 *',
                  hintText: '请输入物品名称',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入物品名称';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // 英文名
              TextFormField(
                controller: _englishNameController,
                decoration: const InputDecoration(
                  labelText: '英文名',
                  hintText: '请输入英文名称',
                ),
              ),

              const SizedBox(height: 16),

              // 型号
              TextFormField(
                controller: _modelController,
                decoration: const InputDecoration(
                  labelText: '型号 *',
                  hintText: '请输入型号',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入型号';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // 价格
              TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(
                  labelText: '价格',
                  hintText: '请输入价格',
                  prefixText: '¥ ',
                ),
                keyboardType: TextInputType.number,
              ),

              const SizedBox(height: 16),

              // 分类选择
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  labelText: '分类 *',
                  hintText: '请选择分类',
                ),
                items: const [
                  DropdownMenuItem(value: '机修', child: Text('机修')),
                  DropdownMenuItem(value: '空调', child: Text('空调')),
                  DropdownMenuItem(value: '强电', child: Text('强电')),
                  DropdownMenuItem(value: '弱电', child: Text('弱电')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return '请选择分类';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // 供应商选择
              DropdownButtonFormField<String>(
                value: _selectedSupplier,
                decoration: const InputDecoration(
                  labelText: '供应商',
                  hintText: '请选择供应商',
                ),
                items: const [
                  DropdownMenuItem(value: '供应商A', child: Text('供应商A')),
                  DropdownMenuItem(value: '供应商B', child: Text('供应商B')),
                  DropdownMenuItem(value: '供应商C', child: Text('供应商C')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedSupplier = value;
                  });
                },
              ),

              const SizedBox(height: 16),

              // 货架位置
              TextFormField(
                controller: _shelfLocationController,
                decoration: const InputDecoration(
                  labelText: '货架位置',
                  hintText: '例如：A-01-01',
                ),
              ),

              const SizedBox(height: 16),

              // 数量选择
              Text('数量 *', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 8),
              QuantitySelector(
                quantity: _quantity,
                onChanged: _onQuantityChanged,
              ),

              const SizedBox(height: 16),

              // 图片选择
              Text(
                '物品图片（最多5张）',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              ImagePickerWidget(
                selectedImages: _selectedImages,
                onImagesSelected: _onImagesSelected,
                maxImages: 5,
              ),

              const SizedBox(height: 32),

              // 提交按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _submitForm,
                  child: const Text('添加物品'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
