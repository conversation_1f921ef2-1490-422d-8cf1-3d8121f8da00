import 'package:flutter/material.dart';
import 'message_list_screen.dart';
import 'category_management_screen.dart';
import 'user_management_screen.dart';
import 'warehouse_management_screen.dart';
import 'batch_operation_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(title: const Text('设置')),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),

            // 消息列表
            _buildSettingItem(
              context,
              icon: Icons.notifications_outlined,
              title: '消息列表',
              subtitle: '查看系统消息和通知',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const MessageListScreen(),
                  ),
                );
              },
            ),

            // 分类管理
            _buildSettingItem(
              context,
              icon: Icons.category_outlined,
              title: '分类管理',
              subtitle: '管理物品分类',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CategoryManagementScreen(),
                  ),
                );
              },
            ),

            // 用户管理
            _buildSettingItem(
              context,
              icon: Icons.people_outlined,
              title: '用户管理',
              subtitle: '管理用户权限',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const UserManagementScreen(),
                  ),
                );
              },
            ),

            // 仓库管理
            _buildSettingItem(
              context,
              icon: Icons.warehouse_outlined,
              title: '仓库管理',
              subtitle: '管理仓库信息',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WarehouseManagementScreen(),
                  ),
                );
              },
            ),

            // 批量操作
            _buildSettingItem(
              context,
              icon: Icons.upload_file_outlined,
              title: '批量操作',
              subtitle: '批量导入物品数据',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const BatchOperationScreen(),
                  ),
                );
              },
            ),

            const Divider(height: 32),

            // 加入组织
            _buildSettingItem(
              context,
              icon: Icons.group_add_outlined,
              title: '加入组织',
              subtitle: '输入组织代码加入',
              onTap: () {
                _showJoinOrganizationDialog(context);
              },
            ),

            // 分享组织
            _buildSettingItem(
              context,
              icon: Icons.share_outlined,
              title: '分享组织',
              subtitle: '生成组织邀请码',
              onTap: () {
                _showShareOrganizationDialog(context);
              },
            ),

            // 关于
            _buildSettingItem(
              context,
              icon: Icons.info_outlined,
              title: '关于',
              subtitle: '应用信息',
              onTap: () {
                _showAboutDialog(context);
              },
            ),

            const SizedBox(height: 32),

            // 退出登录
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _showLogoutDialog(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[50],
                  foregroundColor: Colors.red,
                ),
                child: const Text('退出登录'),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  void _showJoinOrganizationDialog(BuildContext context) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('加入组织'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '组织代码',
            hintText: '请输入组织邀请代码',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现加入组织逻辑
              Navigator.pop(context);
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('加入组织成功')));
            },
            child: const Text('加入'),
          ),
        ],
      ),
    );
  }

  void _showShareOrganizationDialog(BuildContext context) {
    const organizationCode = 'ORG-123456'; // 示例代码

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('分享组织'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('组织邀请代码：'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                organizationCode,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现分享功能
              Navigator.pop(context);
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('邀请码已复制到剪贴板')));
            },
            child: const Text('复制'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: '仓库管理系统',
      applicationVersion: '1.0.0',
      applicationLegalese: '© 2024 仓库管理系统',
      children: const [Text('一个现代化的仓库管理应用')],
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现退出登录逻辑
              Navigator.pop(context);
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('已退出登录')));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('退出'),
          ),
        ],
      ),
    );
  }
}
