import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart';
import 'dart:io';

class BatchOperationScreen extends StatefulWidget {
  const BatchOperationScreen({super.key});

  @override
  State<BatchOperationScreen> createState() => _BatchOperationScreenState();
}

class _BatchOperationScreenState extends State<BatchOperationScreen> {
  String? _selectedFilePath;
  bool _isUploading = false;

  Future<void> _downloadTemplate() async {
    try {
      // 从assets复制模板文件到下载目录
      final ByteData data = await rootBundle.load('assets/templates/batch_import_template.csv');
      final List<int> bytes = data.buffer.asUint8List();
      
      final Directory? downloadsDir = await getDownloadsDirectory();
      if (downloadsDir != null) {
        final String filePath = '${downloadsDir.path}/物品导入模板.csv';
        final File file = File(filePath);
        await file.writeAsBytes(bytes);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('模板文件已下载到：$filePath')),
          );
        }
      } else {
        // 如果无法获取下载目录，尝试使用应用文档目录
        final Directory appDocDir = await getApplicationDocumentsDirectory();
        final String filePath = '${appDocDir.path}/物品导入模板.csv';
        final File file = File(filePath);
        await file.writeAsBytes(bytes);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('模板文件已保存到：$filePath')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('下载失败：$e')),
        );
      }
    }
  }

  Future<void> _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'xlsx', 'xls'],
      );

      if (result != null) {
        setState(() {
          _selectedFilePath = result.files.single.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('文件选择失败：$e')),
        );
      }
    }
  }

  Future<void> _uploadFile() async {
    if (_selectedFilePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择文件')),
      );
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      // TODO: 实现文件上传和批量导入逻辑
      // 这里预留接口，目前只是模拟上传过程
      await Future.delayed(const Duration(seconds: 2));
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('批量导入成功')),
        );
        setState(() {
          _selectedFilePath = null;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('上传失败：$e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('批量操作'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 说明文字
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '批量导入说明',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '1. 下载模板文件\n'
                      '2. 按照模板格式填写物品信息\n'
                      '3. 上传填写好的文件\n'
                      '4. 系统将自动导入物品数据',
                      style: TextStyle(height: 1.5),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 下载模板
            Text(
              '第一步：下载模板文件',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _downloadTemplate,
                icon: const Icon(Icons.download),
                label: const Text('下载Excel模板'),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // 上传文件
            Text(
              '第二步：上传填写好的文件',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            
            // 文件选择区域
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.grey[300]!,
                  style: BorderStyle.solid,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.cloud_upload_outlined,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 12),
                  if (_selectedFilePath != null) ...[
                    Text(
                      '已选择文件：',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _selectedFilePath!.split('/').last,
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    const SizedBox(height: 12),
                  ] else ...[
                    Text(
                      '点击选择文件',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '支持 CSV、Excel 格式',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(height: 12),
                  ],
                  ElevatedButton.icon(
                    onPressed: _pickFile,
                    icon: const Icon(Icons.folder_open),
                    label: Text(_selectedFilePath != null ? '重新选择' : '选择文件'),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 上传按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isUploading ? null : _uploadFile,
                icon: _isUploading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.upload),
                label: Text(_isUploading ? '上传中...' : '开始导入'),
              ),
            ),
            
            const Spacer(),
            
            // 注意事项
            Card(
              color: Colors.orange[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.warning_amber_outlined,
                          color: Colors.orange[700],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '注意事项',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.orange[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• 请确保文件格式正确\n'
                      '• 必填字段不能为空\n'
                      '• 分类和供应商必须已存在\n'
                      '• 建议单次导入不超过1000条数据',
                      style: TextStyle(
                        height: 1.5,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
