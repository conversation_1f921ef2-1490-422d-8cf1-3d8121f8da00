import 'package:equatable/equatable.dart';

class Category extends Equatable {
  final String id;
  final String name;
  final String? parentId;
  final String warehouseId;
  final String iconPath;
  final int level; // 1, 2, 3 for 一级、二级、三级分类
  final DateTime createdAt;
  final DateTime updatedAt;

  const Category({
    required this.id,
    required this.name,
    this.parentId,
    required this.warehouseId,
    required this.iconPath,
    required this.level,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] as String,
      name: json['name'] as String,
      parentId: json['parent_id'] as String?,
      warehouseId: json['warehouse_id'] as String,
      iconPath: json['icon_path'] as String,
      level: json['level'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'parent_id': parentId,
      'warehouse_id': warehouseId,
      'icon_path': iconPath,
      'level': level,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Category copyWith({
    String? id,
    String? name,
    String? parentId,
    String? warehouseId,
    String? iconPath,
    int? level,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      warehouseId: warehouseId ?? this.warehouseId,
      iconPath: iconPath ?? this.iconPath,
      level: level ?? this.level,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        parentId,
        warehouseId,
        iconPath,
        level,
        createdAt,
        updatedAt,
      ];
}
