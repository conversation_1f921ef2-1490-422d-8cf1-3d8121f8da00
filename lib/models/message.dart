import 'package:equatable/equatable.dart';

enum MessageType {
  stockAlert,
  system,
  operation,
}

extension MessageTypeExtension on MessageType {
  String get displayName {
    switch (this) {
      case MessageType.stockAlert:
        return '库存预警';
      case MessageType.system:
        return '系统消息';
      case MessageType.operation:
        return '操作记录';
    }
  }

  String get value {
    switch (this) {
      case MessageType.stockAlert:
        return 'stock_alert';
      case MessageType.system:
        return 'system';
      case MessageType.operation:
        return 'operation';
    }
  }

  static MessageType fromString(String value) {
    switch (value) {
      case 'stock_alert':
        return MessageType.stockAlert;
      case 'system':
        return MessageType.system;
      case 'operation':
        return MessageType.operation;
      default:
        return MessageType.system;
    }
  }
}

class Message extends Equatable {
  final String id;
  final String title;
  final String content;
  final MessageType type;
  final String userId;
  final String warehouseId;
  final bool isRead;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Message({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.userId,
    required this.warehouseId,
    required this.isRead,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      type: MessageTypeExtension.fromString(json['type'] as String),
      userId: json['user_id'] as String,
      warehouseId: json['warehouse_id'] as String,
      isRead: json['is_read'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type.value,
      'user_id': userId,
      'warehouse_id': warehouseId,
      'is_read': isRead,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Message copyWith({
    String? id,
    String? title,
    String? content,
    MessageType? type,
    String? userId,
    String? warehouseId,
    bool? isRead,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Message(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      userId: userId ?? this.userId,
      warehouseId: warehouseId ?? this.warehouseId,
      isRead: isRead ?? this.isRead,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        content,
        type,
        userId,
        warehouseId,
        isRead,
        metadata,
        createdAt,
        updatedAt,
      ];
}
