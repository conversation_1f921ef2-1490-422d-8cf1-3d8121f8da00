import 'package:equatable/equatable.dart';

class Item extends Equatable {
  final String id;
  final String name;
  final String? englishName;
  final String model;
  final double price;
  final String categoryId;
  final String? supplierId;
  final int quantity;
  final String shelfLocation;
  final int alertQuantity;
  final List<String> imageUrls;
  final String warehouseId;
  final DateTime? lastInboundTime;
  final DateTime? lastOutboundTime;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Item({
    required this.id,
    required this.name,
    this.englishName,
    required this.model,
    required this.price,
    required this.categoryId,
    this.supplierId,
    required this.quantity,
    required this.shelfLocation,
    required this.alertQuantity,
    required this.imageUrls,
    required this.warehouseId,
    this.lastInboundTime,
    this.lastOutboundTime,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Item.fromJson(Map<String, dynamic> json) {
    return Item(
      id: json['id'] as String,
      name: json['name'] as String,
      englishName: json['english_name'] as String?,
      model: json['model'] as String,
      price: (json['price'] as num).toDouble(),
      categoryId: json['category_id'] as String,
      supplierId: json['supplier_id'] as String?,
      quantity: json['quantity'] as int,
      shelfLocation: json['shelf_location'] as String,
      alertQuantity: json['alert_quantity'] as int,
      imageUrls: List<String>.from(json['image_urls'] as List? ?? []),
      warehouseId: json['warehouse_id'] as String,
      lastInboundTime: json['last_inbound_time'] != null
          ? DateTime.parse(json['last_inbound_time'] as String)
          : null,
      lastOutboundTime: json['last_outbound_time'] != null
          ? DateTime.parse(json['last_outbound_time'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'english_name': englishName,
      'model': model,
      'price': price,
      'category_id': categoryId,
      'supplier_id': supplierId,
      'quantity': quantity,
      'shelf_location': shelfLocation,
      'alert_quantity': alertQuantity,
      'image_urls': imageUrls,
      'warehouse_id': warehouseId,
      'last_inbound_time': lastInboundTime?.toIso8601String(),
      'last_outbound_time': lastOutboundTime?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Item copyWith({
    String? id,
    String? name,
    String? englishName,
    String? model,
    double? price,
    String? categoryId,
    String? supplierId,
    int? quantity,
    String? shelfLocation,
    int? alertQuantity,
    List<String>? imageUrls,
    String? warehouseId,
    DateTime? lastInboundTime,
    DateTime? lastOutboundTime,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Item(
      id: id ?? this.id,
      name: name ?? this.name,
      englishName: englishName ?? this.englishName,
      model: model ?? this.model,
      price: price ?? this.price,
      categoryId: categoryId ?? this.categoryId,
      supplierId: supplierId ?? this.supplierId,
      quantity: quantity ?? this.quantity,
      shelfLocation: shelfLocation ?? this.shelfLocation,
      alertQuantity: alertQuantity ?? this.alertQuantity,
      imageUrls: imageUrls ?? this.imageUrls,
      warehouseId: warehouseId ?? this.warehouseId,
      lastInboundTime: lastInboundTime ?? this.lastInboundTime,
      lastOutboundTime: lastOutboundTime ?? this.lastOutboundTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        englishName,
        model,
        price,
        categoryId,
        supplierId,
        quantity,
        shelfLocation,
        alertQuantity,
        imageUrls,
        warehouseId,
        lastInboundTime,
        lastOutboundTime,
        createdAt,
        updatedAt,
      ];
}
