import 'package:flutter/material.dart';

class FilterBar extends StatelessWidget {
  final String sortBy;
  final bool isAscending;
  final Function(String, bool) onSortChanged;
  final VoidCallback onReset;

  const FilterBar({
    super.key,
    required this.sortBy,
    required this.isAscending,
    required this.onSortChanged,
    required this.onReset,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.grey[50],
      child: Row(
        children: [
          // 排序选择
          Expanded(
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: sortBy,
                icon: const Icon(Icons.sort),
                items: const [
                  DropdownMenuItem(value: 'name', child: Text('按名称')),
                  DropdownMenuItem(value: 'model', child: Text('按型号')),
                  DropdownMenuItem(value: 'supplier', child: Text('按供应商')),
                  DropdownMenuItem(value: 'price', child: Text('按价格')),
                  DropdownMenuItem(value: 'quantity', child: Text('按数量')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    onSortChanged(value, isAscending);
                  }
                },
              ),
            ),
          ),
          
          // 排序方向
          IconButton(
            onPressed: () {
              onSortChanged(sortBy, !isAscending);
            },
            icon: Icon(
              isAscending ? Icons.arrow_upward : Icons.arrow_downward,
            ),
            tooltip: isAscending ? '升序' : '降序',
          ),
          
          // 重置按钮
          TextButton.icon(
            onPressed: onReset,
            icon: const Icon(Icons.refresh),
            label: const Text('重置'),
          ),
        ],
      ),
    );
  }
}
