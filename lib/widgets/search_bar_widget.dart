import 'package:flutter/material.dart';

class SearchBarWidget extends StatefulWidget {
  final TextEditingController controller;
  final ValueChanged<String> onChanged;
  final String hintText;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.onChanged,
    required this.hintText,
  });

  @override
  State<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> {
  bool _showSuggestions = false;
  final List<String> _suggestions = [
    '螺丝刀',
    '空调滤网',
    '电缆线',
    '开关',
    '插座',
    '灯泡',
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextField(
          controller: widget.controller,
          onChanged: (value) {
            widget.onChanged(value);
            setState(() {
              _showSuggestions = value.isNotEmpty;
            });
          },
          decoration: InputDecoration(
            hintText: widget.hintText,
            prefixIcon: const Icon(Icons.search),
            suffixIcon: widget.controller.text.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      widget.controller.clear();
                      widget.onChanged('');
                      setState(() {
                        _showSuggestions = false;
                      });
                    },
                    icon: const Icon(Icons.clear),
                  )
                : null,
          ),
        ),
        
        // 搜索建议
        if (_showSuggestions && widget.controller.text.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: _suggestions
                  .where((suggestion) => suggestion
                      .toLowerCase()
                      .contains(widget.controller.text.toLowerCase()))
                  .take(5)
                  .map((suggestion) => ListTile(
                        dense: true,
                        leading: const Icon(Icons.search, size: 20),
                        title: Text(suggestion),
                        onTap: () {
                          widget.controller.text = suggestion;
                          widget.onChanged(suggestion);
                          setState(() {
                            _showSuggestions = false;
                          });
                        },
                      ))
                  .toList(),
            ),
          ),
      ],
    );
  }
}
