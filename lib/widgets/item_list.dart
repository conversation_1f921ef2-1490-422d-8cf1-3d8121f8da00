import 'package:flutter/material.dart';
import 'item_list_tile.dart';

class ItemList extends StatefulWidget {
  final String searchQuery;
  final String sortBy;
  final bool isAscending;

  const ItemList({
    super.key,
    required this.searchQuery,
    required this.sortBy,
    required this.isAscending,
  });

  @override
  State<ItemList> createState() => _ItemListState();
}

class _ItemListState extends State<ItemList> {
  final ScrollController _scrollController = ScrollController();
  final List<Map<String, dynamic>> _allItems = [
    {
      'id': '1',
      'name': '螺丝刀套装',
      'englishName': 'Screwdriver Set',
      'model': 'SD-001',
      'quantity': 25,
      'category': '机修',
      'shelfLocation': 'A-01-01',
      'price': 89.0,
      'supplier': '工具供应商A',
    },
    {
      'id': '2',
      'name': '空调滤网',
      'englishName': 'AC Filter',
      'model': 'ACF-002',
      'quantity': 8,
      'category': '空调',
      'shelfLocation': 'B-02-03',
      'price': 45.0,
      'supplier': '空调配件商B',
    },
    {
      'id': '3',
      'name': '电缆线',
      'englishName': 'Cable Wire',
      'model': 'CW-100',
      'quantity': 150,
      'category': '强电',
      'shelfLocation': 'C-01-05',
      'price': 12.5,
      'supplier': '电气供应商C',
    },
    {
      'id': '4',
      'name': '开关面板',
      'englishName': 'Switch Panel',
      'model': 'SP-201',
      'quantity': 32,
      'category': '弱电',
      'shelfLocation': 'D-03-02',
      'price': 28.0,
      'supplier': '电气供应商C',
    },
    {
      'id': '5',
      'name': 'LED灯泡',
      'englishName': 'LED Bulb',
      'model': 'LED-50W',
      'quantity': 64,
      'category': '照明',
      'shelfLocation': 'E-01-01',
      'price': 15.0,
      'supplier': '照明设备商D',
    },
  ];

  List<Map<String, dynamic>> get _filteredItems {
    var items = _allItems.where((item) {
      if (widget.searchQuery.isEmpty) return true;
      
      final query = widget.searchQuery.toLowerCase();
      return (item['name'] as String).toLowerCase().contains(query) ||
             (item['englishName'] as String?)?.toLowerCase().contains(query) == true ||
             (item['model'] as String).toLowerCase().contains(query) ||
             (item['supplier'] as String).toLowerCase().contains(query);
    }).toList();

    // 排序
    items.sort((a, b) {
      dynamic aValue, bValue;
      
      switch (widget.sortBy) {
        case 'name':
          aValue = a['name'];
          bValue = b['name'];
          break;
        case 'model':
          aValue = a['model'];
          bValue = b['model'];
          break;
        case 'supplier':
          aValue = a['supplier'];
          bValue = b['supplier'];
          break;
        case 'price':
          aValue = a['price'];
          bValue = b['price'];
          break;
        case 'quantity':
          aValue = a['quantity'];
          bValue = b['quantity'];
          break;
        default:
          aValue = a['name'];
          bValue = b['name'];
      }

      int comparison = aValue.compareTo(bValue);
      return widget.isAscending ? comparison : -comparison;
    });

    return items;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredItems = _filteredItems;

    if (filteredItems.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '没有找到相关物品',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        final item = filteredItems[index];
        return ItemListTile(
          id: item['id'] as String,
          name: item['name'] as String,
          englishName: item['englishName'] as String?,
          model: item['model'] as String,
          quantity: item['quantity'] as int,
          category: item['category'] as String,
          shelfLocation: item['shelfLocation'] as String,
          imageUrl: null, // 示例数据中没有图片
        );
      },
    );
  }
}
