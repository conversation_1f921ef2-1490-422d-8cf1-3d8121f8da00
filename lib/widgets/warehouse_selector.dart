import 'package:flutter/material.dart';

class WarehouseSelector extends StatefulWidget {
  const WarehouseSelector({super.key});

  @override
  State<WarehouseSelector> createState() => _WarehouseSelectorState();
}

class _WarehouseSelectorState extends State<WarehouseSelector> {
  String _selectedWarehouse = '工程仓';
  
  final List<String> _warehouses = ['工程仓', '财务仓'];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedWarehouse,
          icon: const Icon(Icons.keyboard_arrow_down),
          isExpanded: true,
          items: _warehouses.map((String warehouse) {
            return DropdownMenuItem<String>(
              value: warehouse,
              child: Row(
                children: [
                  Icon(
                    Icons.warehouse,
                    size: 20,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    warehouse,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _selectedWarehouse = newValue;
              });
              // TODO: 实现仓库切换逻辑
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('已切换到$newValue')),
              );
            }
          },
        ),
      ),
    );
  }
}
