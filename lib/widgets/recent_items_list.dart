import 'package:flutter/material.dart';
import 'item_list_tile.dart';

class RecentItemsList extends StatelessWidget {
  const RecentItemsList({super.key});

  @override
  Widget build(BuildContext context) {
    // 示例数据
    final recentItems = [
      {
        'id': '1',
        'name': '螺丝刀套装',
        'englishName': 'Screwdriver Set',
        'model': 'SD-001',
        'quantity': 25,
        'category': '机修',
        'shelfLocation': 'A-01-01',
        'imageUrl': null,
        'lastOperation': '入库',
        'operationTime': '2小时前',
      },
      {
        'id': '2',
        'name': '空调滤网',
        'englishName': 'AC Filter',
        'model': 'ACF-002',
        'quantity': 8,
        'category': '空调',
        'shelfLocation': 'B-02-03',
        'imageUrl': null,
        'lastOperation': '出库',
        'operationTime': '4小时前',
      },
      {
        'id': '3',
        'name': '电缆线',
        'englishName': 'Cable Wire',
        'model': 'CW-100',
        'quantity': 150,
        'category': '强电',
        'shelfLocation': 'C-01-05',
        'imageUrl': null,
        'lastOperation': '编辑',
        'operationTime': '1天前',
      },
    ];

    return Column(
      children: [
        ...recentItems.map((item) => ItemListTile(
          id: item['id'] as String,
          name: item['name'] as String,
          englishName: item['englishName'] as String?,
          model: item['model'] as String,
          quantity: item['quantity'] as int,
          category: item['category'] as String,
          shelfLocation: item['shelfLocation'] as String,
          imageUrl: item['imageUrl'] as String?,
          showOperationButtons: false,
          subtitle: '${item['lastOperation']} • ${item['operationTime']}',
        )),
        
        // 查看更多按钮
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: TextButton(
            onPressed: () {
              // TODO: 导航到完整的物品列表
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('查看更多物品')),
              );
            },
            child: const Text('查看更多'),
          ),
        ),
      ],
    );
  }
}
