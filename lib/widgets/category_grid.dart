import 'package:flutter/material.dart';

class CategoryGrid extends StatelessWidget {
  const CategoryGrid({super.key});

  @override
  Widget build(BuildContext context) {
    final categories = [
      {'name': '机修', 'icon': Icons.build, 'color': Colors.blue},
      {'name': '空调', 'icon': Icons.ac_unit, 'color': Colors.cyan},
      {'name': '强电', 'icon': Icons.electrical_services, 'color': Colors.orange},
      {'name': '弱电', 'icon': Icons.cable, 'color': Colors.purple},
      {'name': '消防', 'icon': Icons.local_fire_department, 'color': Colors.red},
      {'name': '安防', 'icon': Icons.security, 'color': Colors.green},
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.0,
        ),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          return _buildCategoryItem(
            context,
            name: category['name'] as String,
            icon: category['icon'] as IconData,
            color: category['color'] as Color,
          );
        },
      ),
    );
  }

  Widget _buildCategoryItem(
    BuildContext context, {
    required String name,
    required IconData icon,
    required Color color,
  }) {
    return GestureDetector(
      onTap: () {
        // TODO: 导航到二级分类页面
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('点击了$name分类')));
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            const SizedBox(height: 8),
            Text(
              name,
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }
}
