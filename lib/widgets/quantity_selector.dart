import 'package:flutter/material.dart';

class QuantitySelector extends StatelessWidget {
  final int quantity;
  final ValueChanged<int> onChanged;
  final int minQuantity;
  final int maxQuantity;

  const QuantitySelector({
    super.key,
    required this.quantity,
    required this.onChanged,
    this.minQuantity = 1,
    this.maxQuantity = 9999,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 减少按钮
        IconButton(
          onPressed: quantity > minQuantity
              ? () => onChanged(quantity - 1)
              : null,
          icon: const Icon(Icons.remove_circle_outline),
          style: IconButton.styleFrom(
            backgroundColor: Colors.grey[100],
            foregroundColor: quantity > minQuantity ? Colors.red : Colors.grey,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // 数量输入框
        SizedBox(
          width: 80,
          child: TextFormField(
            initialValue: quantity.toString(),
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.symmetric(vertical: 12),
            ),
            onChanged: (value) {
              final newQuantity = int.tryParse(value);
              if (newQuantity != null && 
                  newQuantity >= minQuantity && 
                  newQuantity <= maxQuantity) {
                onChanged(newQuantity);
              }
            },
            validator: (value) {
              final quantity = int.tryParse(value ?? '');
              if (quantity == null) {
                return '请输入数字';
              }
              if (quantity < minQuantity) {
                return '最小值$minQuantity';
              }
              if (quantity > maxQuantity) {
                return '最大值$maxQuantity';
              }
              return null;
            },
          ),
        ),
        
        const SizedBox(width: 16),
        
        // 增加按钮
        IconButton(
          onPressed: quantity < maxQuantity
              ? () => onChanged(quantity + 1)
              : null,
          icon: const Icon(Icons.add_circle_outline),
          style: IconButton.styleFrom(
            backgroundColor: Colors.grey[100],
            foregroundColor: quantity < maxQuantity ? Colors.green : Colors.grey,
          ),
        ),
        
        const Spacer(),
        
        // 快捷数量按钮
        Wrap(
          spacing: 8,
          children: [10, 50, 100].map((quickQuantity) {
            return ActionChip(
              label: Text('+$quickQuantity'),
              onPressed: () {
                final newQuantity = quantity + quickQuantity;
                if (newQuantity <= maxQuantity) {
                  onChanged(newQuantity);
                }
              },
              backgroundColor: Colors.grey[100],
            );
          }).toList(),
        ),
      ],
    );
  }
}
